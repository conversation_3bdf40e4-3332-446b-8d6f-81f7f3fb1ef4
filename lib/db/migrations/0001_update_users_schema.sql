-- Migration: Update schema to match <PERSON>pa<PERSON> Auth UUIDs
-- This migration safely transitions from integer IDs to UUID IDs

-- Step 1: Check if sync_status enum exists, create if not
DO $$ BEGIN
    CREATE TYPE sync_status AS ENUM ('synced', 'pending', 'conflict');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 2: Create temporary tables with new UUID schema
CREATE TABLE users_new (
  id UUID PRIMARY KEY,
  full_name TEXT,
  phone VARCHAR(256),
  email VARCHAR(256) UNIQUE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE TABLE recipes_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users_new(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  original_portions INTEGER NOT NULL,
  description TEXT
);

CREATE TABLE products_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users_new(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  quantity REAL NOT NULL,
  kcal REAL NOT NULL
);

CREATE TABLE ingredients_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users_new(id) ON DELETE CASCADE,
  recipe_id UUID NOT NULL REFERENCES recipes_new(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  quantity REAL NOT NULL,
  unit TEXT NOT NULL,
  "order" INTEGER DEFAULT 0 NOT NULL
);

-- Step 3: Drop old tables (data will be lost, but this is for development)
DROP TABLE IF EXISTS ingredients CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS recipes CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Step 4: Rename new tables to original names
ALTER TABLE users_new RENAME TO users;
ALTER TABLE recipes_new RENAME TO recipes;
ALTER TABLE products_new RENAME TO products;
ALTER TABLE ingredients_new RENAME TO ingredients;

-- Step 5: Create indexes for better performance
CREATE INDEX idx_products_user_id ON products(user_id);
CREATE INDEX idx_recipes_user_id ON recipes(user_id);
CREATE INDEX idx_ingredients_user_id ON ingredients(user_id);
CREATE INDEX idx_ingredients_recipe_id ON ingredients(recipe_id);
CREATE INDEX idx_sync_status ON products(sync_status);
CREATE INDEX idx_recipes_sync_status ON recipes(sync_status);
CREATE INDEX idx_ingredients_sync_status ON ingredients(sync_status);
