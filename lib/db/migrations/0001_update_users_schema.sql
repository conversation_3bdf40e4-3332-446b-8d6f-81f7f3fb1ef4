-- Migration: Update users table to match <PERSON><PERSON><PERSON> Auth
-- Drop existing users table and recreate with proper <PERSON><PERSON><PERSON> primary key

-- First, drop dependent tables to avoid foreign key constraints
DROP TABLE IF EXISTS ingredients CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS recipes CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Recreate users table with UUID primary key (matching <PERSON><PERSON><PERSON> Auth)
CREATE TABLE users (
  id UUID PRIMARY KEY,
  full_name TEXT,
  phone VARCHAR(256),
  email VARCHAR(256) UNIQUE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Recreate sync status enum
CREATE TYPE sync_status AS ENUM ('synced', 'pending', 'conflict');

-- Recreate recipes table
CREATE TABLE recipes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  original_portions INTEGER NOT NULL,
  description TEXT
);

-- Recreate products table
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  quantity REAL NOT NULL,
  kcal REAL NOT NULL
);

-- Recreate ingredients table
CREATE TABLE ingredients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  sync_status sync_status DEFAULT 'synced' NOT NULL,
  last_sync_at TIMESTAMP,
  version INTEGER DEFAULT 1 NOT NULL,
  is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
  name TEXT NOT NULL,
  quantity REAL NOT NULL,
  unit TEXT NOT NULL,
  "order" INTEGER DEFAULT 0 NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_products_user_id ON products(user_id);
CREATE INDEX idx_recipes_user_id ON recipes(user_id);
CREATE INDEX idx_ingredients_user_id ON ingredients(user_id);
CREATE INDEX idx_ingredients_recipe_id ON ingredients(recipe_id);
CREATE INDEX idx_sync_status ON products(sync_status);
CREATE INDEX idx_recipes_sync_status ON recipes(sync_status);
CREATE INDEX idx_ingredients_sync_status ON ingredients(sync_status);
