import { eq } from "drizzle-orm";
import type { User as SupabaseUser } from "@supabase/supabase-js";

import { db } from "./index";
import { users, type User } from "./schema";

/**
 * Create or update user in database when they sign up/sign in via Supabase Auth
 */
export async function upsertUser(supabaseUser: SupabaseUser): Promise<User> {
  const userData = {
    id: supabaseUser.id,
    email: supabaseUser.email || null,
    fullName: supabaseUser.user_metadata?.full_name || 
              supabaseUser.user_metadata?.name || 
              supabaseUser.email?.split("@")[0] || null,
    phone: supabaseUser.user_metadata?.phone || null,
    updatedAt: new Date(),
  };

  // Try to update existing user first
  const existingUsers = await db
    .update(users)
    .set(userData)
    .where(eq(users.id, supabaseUser.id))
    .returning();

  if (existingUsers.length > 0) {
    return existingUsers[0];
  }

  // If user doesn't exist, create new one
  const newUsers = await db
    .insert(users)
    .values({
      ...userData,
      createdAt: new Date(),
    })
    .returning();

  return newUsers[0];
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string): Promise<User | null> {
  const result = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  return result[0] || null;
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  const result = await db
    .select()
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  return result[0] || null;
}

/**
 * Update user profile
 */
export async function updateUserProfile(
  userId: string,
  updates: Partial<Pick<User, "fullName" | "phone">>
): Promise<User | null> {
  const result = await db
    .update(users)
    .set({
      ...updates,
      updatedAt: new Date(),
    })
    .where(eq(users.id, userId))
    .returning();

  return result[0] || null;
}

/**
 * Delete user and all associated data
 */
export async function deleteUser(userId: string): Promise<boolean> {
  try {
    await db.delete(users).where(eq(users.id, userId));
    return true;
  } catch (error) {
    console.error("Error deleting user:", error);
    return false;
  }
}
